# Flutter Flavors Setup

This project uses Flutter flavors to manage different environments (Development, Staging, Production).

## Available Flavors

### Development
- **App Name**: Doctor Dev
- **Package ID**: com.example.doctor.dev
- **API Base URL**: https://dev-api.doctor.com/api/
- **Debug Mode**: Enabled
- **Banner**: Red banner with "DEVELOPMENT ENVIRONMENT"

### Staging
- **App Name**: Doctor Staging
- **Package ID**: com.example.doctor.staging
- **API Base URL**: https://staging-api.doctor.com/api/
- **Debug Mode**: Enabled
- **Banner**: Orange banner with "STAGING ENVIRONMENT"

### Production
- **App Name**: Doctor
- **Package ID**: com.example.doctor
- **API Base URL**: https://api.doctor.com/api/
- **Debug Mode**: Disabled
- **Banner**: No banner

## Running the App

### Using VS Code
1. Open the project in VS Code
2. Go to Run and Debug (Ctrl+Shift+D)
3. Select the desired flavor from the dropdown:
   - Development
   - Staging
   - Production
4. Press F5 or click the play button

### Using Command Line

#### Development
```bash
flutter run --flavor development --target lib/main_development.dart
```

#### Staging
```bash
flutter run --flavor staging --target lib/main_staging.dart
```

#### Production
```bash
flutter run --flavor production --target lib/main_production.dart
```

### Building APKs

#### Development
```bash
flutter build apk --flavor development --target lib/main_development.dart
```

#### Staging
```bash
flutter build apk --flavor staging --target lib/main_staging.dart
```

#### Production
```bash
flutter build apk --flavor production --target lib/main_production.dart
```

## Configuration

### Adding New Environments
1. Update `FlavorConfig` in `lib/core/config/flavor_config.dart`
2. Add new flavor to Android `build.gradle.kts`
3. Create new main entry point (e.g., `main_newenv.dart`)
4. Add launch configuration in `.vscode/launch.json`

### Updating API URLs
Update the base URLs in `FlavorConfig._setFlavorValues()` method.

### Customizing App Names
Update app names in both:
- `FlavorConfig._setFlavorValues()` method
- Android `build.gradle.kts` productFlavors section

## File Structure

```
lib/
├── main.dart                    # Production entry point (default)
├── main_development.dart        # Development entry point
├── main_staging.dart           # Staging entry point
├── main_production.dart        # Production entry point
└── core/
    ├── config/
    │   └── flavor_config.dart   # Flavor configuration
    └── widgets/
        └── flavor_banner.dart   # Environment banner widget
```

## Features

- **Environment-specific API URLs**: Each flavor uses different backend endpoints
- **Visual Environment Indicators**: Non-production builds show colored banners
- **Separate App Icons**: Each flavor can have different app icons (configure in platform-specific folders)
- **Different Package IDs**: Allows installing multiple versions simultaneously
- **Debug/Release Configurations**: Production disables debug features
