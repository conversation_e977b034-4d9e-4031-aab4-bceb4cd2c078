import 'package:flutter/material.dart';
import '../config/flavor_config.dart';
import '../theming/colors.dart';

class FlavorBanner extends StatelessWidget {
  final Widget child;
  
  const FlavorBanner({
    super.key,
    required this.child,
  });

  @override
  Widget build(BuildContext context) {
    if (!FlavorConfig.showFlavorBanner) {
      return child;
    }

    return Stack(
      children: [
        child,
        Positioned(
          top: 0,
          left: 0,
          right: 0,
          child: SafeArea(
            child: Container(
              height: 30,
              color: _getBannerColor(),
              child: Center(
                child: Text(
                  FlavorConfig.flavorBanner,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Color _getBannerColor() {
    switch (FlavorConfig.flavor) {
      case Flavor.development:
        return Colors.red;
      case Flavor.staging:
        return Colors.orange;
      case Flavor.production:
      default:
        return ColorsManager.mainBlue;
    }
  }
}
