enum Flavor {
  development,
  staging,
  production,
}

class FlavorConfig {
  static Flavor? _flavor;
  static String? _name;
  static String? _baseUrl;
  static String? _appName;
  static bool? _isDebug;

  static Flavor? get flavor => _flavor;
  static String? get name => _name;
  static String? get baseUrl => _baseUrl;
  static String? get appName => _appName;
  static bool? get isDebug => _isDebug;

  static bool get isDevelopment => _flavor == Flavor.development;
  static bool get isStaging => _flavor == Flavor.staging;
  static bool get isProduction => _flavor == Flavor.production;

  static void setFlavor(Flavor flavor) {
    _flavor = flavor;
    _setFlavorValues(flavor);
  }

  static void _setFlavorValues(Flavor flavor) {
    switch (flavor) {
      case Flavor.development:
        _name = 'Development';
        _baseUrl = 'https://dev-api.doctor.com/api/';
        _appName = 'Doctor Dev';
        _isDebug = true;
        break;
      case Flavor.staging:
        _name = 'Staging';
        _baseUrl = 'https://staging-api.doctor.com/api/';
        _appName = 'Doctor Staging';
        _isDebug = true;
        break;
      case Flavor.production:
        _name = 'Production';
        _baseUrl = 'https://api.doctor.com/api/';
        _appName = 'Doctor';
        _isDebug = false;
        break;
    }
  }

  static String get flavorBanner {
    if (isProduction) return '';
    return '${_name?.toUpperCase()} ENVIRONMENT';
  }

  static bool get showFlavorBanner => !isProduction;
}
