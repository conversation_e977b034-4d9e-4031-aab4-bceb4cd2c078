import 'package:doctor/core/config/flavor_config.dart';
import 'package:doctor/doc_app.dart';
import 'package:flutter/material.dart';

import 'core/di/dependancy_injection.dart';
import 'core/routing/app_router.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Set the flavor to production by default
  FlavorConfig.setFlavor(Flavor.production);

  // Setup dependency injection
  await setupGetIt();

  runApp(DocApp(appRouter: AppRouter()));
}
